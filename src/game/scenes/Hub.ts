import { Scene } from "phaser";
import { Player, MovementMode } from "../entities/Player";
import { BulletManager } from "../systems/BulletManager";
import { InputSystem } from "../systems/InputSystem";
import { ResolutionManager } from "../systems/ResolutionManager";

class Hub extends Scene {
  private player!: Player;
  private bulletManager!: BulletManager;
  private inputSystem!: InputSystem;
  private resolutionManager!: ResolutionManager;

  // Hub world size (5x the virtual resolution)
  private readonly HUB_WORLD_WIDTH = 1600;
  private readonly HUB_WORLD_HEIGHT = 1200;

  constructor() {
    super("Hub");
  }

  create() {
    // Initialize systems
    this.inputSystem = InputSystem.getInstance();
    this.resolutionManager = ResolutionManager.getInstance();

    // Set world bounds to the larger Hub area (using Matter.js)
    this.matter.world.setBounds(
      0,
      0,
      this.HUB_WORLD_WIDTH,
      this.HUB_WORLD_HEIGHT
    );

    // Initialize bullet manager
    this.bulletManager = new BulletManager(this);

    // Create player at the center of the Hub world
    const playerStartX = this.HUB_WORLD_WIDTH / 2;
    const playerStartY = this.HUB_WORLD_HEIGHT / 2;
    this.player = new Player(this, playerStartX, playerStartY);

    // Set player to Free movement mode for the Hub
    this.player.setMovementMode(MovementMode.Free);

    // Connect bullet manager to player
    this.player.setBulletManager(this.bulletManager);

    // Configure camera to follow the player
    this.setupCamera();

    // Add some visual boundaries (optional - just for reference)
    this.createHubBoundaries();
  }

  private setupCamera(): void {
    const virtualRes = this.resolutionManager.getVirtualResolution();

    // Make the camera follow the player
    this.cameras.main.startFollow(this.player);

    // Set camera bounds to the Hub world size
    this.cameras.main.setBounds(
      0,
      0,
      this.HUB_WORLD_WIDTH,
      this.HUB_WORLD_HEIGHT
    );

    // Center the player in the camera view with no deadzone for immediate following
    this.cameras.main.setDeadzone(0, 0);

    // Set camera zoom to match the virtual resolution scaling
    const scaleFactor = this.resolutionManager.getScaleFactor();
    this.cameras.main.setZoom(scaleFactor);
  }

  private createHubBoundaries(): void {
    // Create subtle boundary markers at the edges of the Hub world
    const graphics = this.add.graphics();
    graphics.lineStyle(2, 0x333333, 0.5);

    // Draw a rectangle around the Hub boundaries
    graphics.strokeRect(0, 0, this.HUB_WORLD_WIDTH, this.HUB_WORLD_HEIGHT);

    // Add some corner markers for better visibility
    const cornerSize = 20;
    graphics.lineStyle(3, 0x666666, 0.8);

    // Top-left corner
    graphics.moveTo(0, 0);
    graphics.lineTo(cornerSize, 0);
    graphics.moveTo(0, 0);
    graphics.lineTo(0, cornerSize);

    // Top-right corner
    graphics.moveTo(this.HUB_WORLD_WIDTH - cornerSize, 0);
    graphics.lineTo(this.HUB_WORLD_WIDTH, 0);
    graphics.moveTo(this.HUB_WORLD_WIDTH, 0);
    graphics.lineTo(this.HUB_WORLD_WIDTH, cornerSize);

    // Bottom-left corner
    graphics.moveTo(0, this.HUB_WORLD_HEIGHT - cornerSize);
    graphics.lineTo(0, this.HUB_WORLD_HEIGHT);
    graphics.moveTo(0, this.HUB_WORLD_HEIGHT);
    graphics.lineTo(cornerSize, this.HUB_WORLD_HEIGHT);

    // Bottom-right corner
    graphics.moveTo(this.HUB_WORLD_WIDTH - cornerSize, this.HUB_WORLD_HEIGHT);
    graphics.lineTo(this.HUB_WORLD_WIDTH, this.HUB_WORLD_HEIGHT);
    graphics.moveTo(this.HUB_WORLD_WIDTH, this.HUB_WORLD_HEIGHT - cornerSize);
    graphics.lineTo(this.HUB_WORLD_WIDTH, this.HUB_WORLD_HEIGHT);

    graphics.strokePath();
  }

  update(time: number, delta: number): void {
    // Update player
    this.player.update(time, delta);

    // Update bullet manager
    this.bulletManager.update(time, delta);

    // Handle debug key to switch to Level scene (for testing)
    if (this.inputSystem.pressed("select")) {
      this.scene.start("Level");
    }
  }
}

export { Hub };
